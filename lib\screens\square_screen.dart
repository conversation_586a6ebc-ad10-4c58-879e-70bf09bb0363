import 'package:flutter/material.dart';
import '../models/character.dart';
import '../models/player_status.dart';
import '../models/facility.dart';
import '../services/logger_service.dart';
import '../widgets/location_bar.dart';
import '../widgets/facility_grid.dart';
import '../widgets/team_bar.dart';
import '../widgets/action_bar.dart';
import 'weapon_shop_screen.dart';

/// 广场主界面
/// 玩家进入游戏世界后的第一个场景，连接各个功能区域的中心枢纽
class SquareScreen extends StatefulWidget {
  final Character character;

  const SquareScreen({
    super.key,
    required this.character,
  });

  @override
  State<SquareScreen> createState() => _SquareScreenState();
}

class _SquareScreenState extends State<SquareScreen> {
  late PlayerStatus _playerStatus;
  Facility? _selectedFacility;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    LoggerService.enterScreen('SquareScreen', {
      'characterId': widget.character.id,
      'characterName': widget.character.name,
    });
    
    _initializePlayerStatus();
  }

  @override
  void dispose() {
    LoggerService.exitScreen('SquareScreen');
    super.dispose();
  }

  /// 初始化玩家状态
  void _initializePlayerStatus() {
    _playerStatus = PlayerStatus(
      character: widget.character,
      location: 'square',
    );
    
    LoggerService.gameEvent('player_enter_square', {
      'characterId': widget.character.id,
      'characterName': widget.character.name,
      'hp': '${_playerStatus.currentHp}/${_playerStatus.maxHp}',
      'mp': '${_playerStatus.currentMp}/${_playerStatus.maxMp}',
    });
  }

  /// 选择设施
  void _selectFacility(Facility facility) {
    LoggerService.userAction('SquareScreen', 'select_facility', {
      'facilityId': facility.id,
      'facilityName': facility.displayName,
    });

    setState(() {
      if (_selectedFacility?.id == facility.id) {
        // 如果点击已选中的设施，则取消选中
        _selectedFacility = null;
      } else {
        // 选中新设施
        _selectedFacility = facility;
      }
    });
  }

  /// 进入设施
  Future<void> _enterFacility() async {
    if (_selectedFacility == null) return;

    LoggerService.userAction('SquareScreen', 'enter_facility', {
      'facilityId': _selectedFacility!.id,
      'facilityName': _selectedFacility!.displayName,
    });

    setState(() {
      _isLoading = true;
    });

    try {
      // 更新玩家位置
      _playerStatus.updateLocation(_selectedFacility!.id);
      
      // 根据设施类型导航到对应界面
      switch (_selectedFacility!.id) {
        case 'shop':
          await _enterWeaponShop();
          break;
        case 'temple':
          _showInfoSnackBar('神殿功能尚未实现');
          break;
        case 'portal':
          _showInfoSnackBar('传送点功能尚未实现');
          break;
        default:
          _showErrorSnackBar('未知设施');
      }
      
      // 取消选中
      setState(() {
        _selectedFacility = null;
      });
      
    } catch (e, stackTrace) {
      LoggerService.error(LogTags.game, 'Failed to enter facility', e, stackTrace);
      _showErrorSnackBar('进入设施失败，请重试');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 进入武器店
  Future<void> _enterWeaponShop() async {
    LoggerService.userAction('SquareScreen', 'enter_weapon_shop');

    try {
      final result = await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => WeaponShopScreen(character: widget.character),
        ),
      );

      // 如果从武器店返回，可以在这里处理返回结果
      if (result != null) {
        // TODO: 处理从武器店返回的数据
        LoggerService.info(LogTags.game, 'Returned from weapon shop', result);
      }

    } catch (e, stackTrace) {
      LoggerService.error(LogTags.game, 'Failed to enter weapon shop', e, stackTrace);
      _showErrorSnackBar('进入武器店失败，请重试');
    }
  }

  /// 打开法术列表
  void _openSpellList() {
    LoggerService.userAction('SquareScreen', 'open_spell_list');
    
    // 检查职业是否可以使用法术
    final canUseMagic = _playerStatus.character.characterClass.name == 'Mage' ||
                       _playerStatus.character.characterClass.name == 'Cleric';
    
    if (!canUseMagic) {
      _showInfoSnackBar('${_playerStatus.character.characterClass.displayName}无法使用法术');
      return;
    }
    
    // TODO: 显示法术列表模态框
    _showInfoSnackBar('法术功能尚未实现');
  }

  /// 打开道具列表
  void _openInventory() {
    LoggerService.userAction('SquareScreen', 'open_inventory');
    
    // TODO: 显示道具列表模态框
    _showInfoSnackBar('道具功能尚未实现');
  }

  /// 打开装备界面
  void _openEquipment() {
    LoggerService.userAction('SquareScreen', 'open_equipment');
    
    // TODO: 显示装备界面模态框
    _showInfoSnackBar('装备功能尚未实现');
  }

  /// 显示成功消息
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// 显示错误消息
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// 显示信息消息
  void _showInfoSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.blue,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            // 1. 地点条（顶部固定）
            LocationBar(
              icon: '🏙️',
              locationName: '广场',
            ),
            
            // 2. 内容区（中间区域，可滚动）
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    // 设施网格
                    FacilityGrid(
                      facilities: Facility.getAvailableFacilities(),
                      selectedFacility: _selectedFacility,
                      onFacilitySelected: _selectFacility,
                    ),
                    
                    const SizedBox(height: 20),
                    
                    // TODO: 其他在线玩家显示区域
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.grey.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Colors.grey.withOpacity(0.3),
                        ),
                      ),
                      child: const Column(
                        children: [
                          Icon(
                            Icons.people,
                            size: 48,
                            color: Colors.grey,
                          ),
                          SizedBox(height: 8),
                          Text(
                            '其他在线玩家',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          SizedBox(height: 4),
                          Text(
                            '多人在线功能尚未实现',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            // 3. 队伍条（底部固定）
            TeamBar(
              playerStatus: _playerStatus,
              // TODO: 添加队友列表
              teammates: const [],
            ),
            
            // 4. 操作区（最底部固定）
            ActionBar(
              selectedFacility: _selectedFacility,
              playerStatus: _playerStatus,
              isLoading: _isLoading,
              onEnterFacility: _enterFacility,
              onOpenSpellList: _openSpellList,
              onOpenInventory: _openInventory,
              onOpenEquipment: _openEquipment,
            ),
          ],
        ),
      ),
    );
  }
}
