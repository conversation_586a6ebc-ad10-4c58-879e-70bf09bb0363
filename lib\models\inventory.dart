import 'weapon.dart';

/// 库存物品类型
enum InventoryItemType {
  weapon,
  armor,
  consumable,
  misc,
}

/// 库存物品基类
abstract class InventoryItem {
  final String id;
  final String name;
  final String icon;
  final int quantity;
  final InventoryItemType type;

  const InventoryItem({
    required this.id,
    required this.name,
    required this.icon,
    required this.quantity,
    required this.type,
  });

  /// 转换为JSON
  Map<String, dynamic> toJson();

  /// 从JSON创建物品
  static InventoryItem fromJson(Map<String, dynamic> json) {
    final type = InventoryItemType.values.firstWhere(
      (t) => t.name == json['type'],
      orElse: () => InventoryItemType.misc,
    );

    switch (type) {
      case InventoryItemType.weapon:
        return WeaponItem.fromJson(json);
      default:
        throw UnimplementedError('Item type $type not implemented yet');
    }
  }
}

/// 武器物品
class WeaponItem extends InventoryItem {
  final Weapon weapon;

  WeaponItem({
    required this.weapon,
    int quantity = 1,
  }) : super(
          id: weapon.id,
          name: weapon.name,
          icon: weapon.icon,
          quantity: quantity,
          type: InventoryItemType.weapon,
        );

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'icon': icon,
      'quantity': quantity,
      'type': type.name,
      'weapon': weapon.toJson(),
    };
  }

  factory WeaponItem.fromJson(Map<String, dynamic> json) {
    return WeaponItem(
      weapon: Weapon.fromJson(json['weapon']),
      quantity: json['quantity'] ?? 1,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is WeaponItem && other.weapon.id == weapon.id;
  }

  @override
  int get hashCode => weapon.id.hashCode;
}

/// 库存管理类
class Inventory {
  final List<InventoryItem> _items;
  final int maxSlots;

  Inventory({
    List<InventoryItem>? items,
    this.maxSlots = 50,
  }) : _items = items ?? [];

  /// 获取所有物品
  List<InventoryItem> get items => List.unmodifiable(_items);

  /// 获取武器列表
  List<WeaponItem> get weapons => _items
      .whereType<WeaponItem>()
      .toList();

  /// 获取当前使用的槽位数
  int get usedSlots => _items.fold(0, (sum, item) => sum + item.quantity);

  /// 获取剩余槽位数
  int get availableSlots => maxSlots - usedSlots;

  /// 检查是否有空间
  bool hasSpace(int quantity) => availableSlots >= quantity;

  /// 检查是否包含指定物品
  bool contains(String itemId) => _items.any((item) => item.id == itemId);

  /// 获取指定物品的数量
  int getQuantity(String itemId) {
    final item = _items.firstWhere(
      (item) => item.id == itemId,
      orElse: () => throw ArgumentError('Item not found: $itemId'),
    );
    return item.quantity;
  }

  /// 添加物品
  bool addItem(InventoryItem newItem) {
    if (!hasSpace(newItem.quantity)) {
      return false;
    }

    // 查找是否已存在相同物品
    final existingIndex = _items.indexWhere((item) => item.id == newItem.id);
    
    if (existingIndex != -1) {
      // 如果已存在，增加数量
      final existingItem = _items[existingIndex];
      final updatedItem = _createUpdatedItem(existingItem, existingItem.quantity + newItem.quantity);
      _items[existingIndex] = updatedItem;
    } else {
      // 如果不存在，添加新物品
      _items.add(newItem);
    }

    return true;
  }

  /// 移除物品
  bool removeItem(String itemId, {int quantity = 1}) {
    final existingIndex = _items.indexWhere((item) => item.id == itemId);
    
    if (existingIndex == -1) {
      return false;
    }

    final existingItem = _items[existingIndex];
    
    if (existingItem.quantity <= quantity) {
      // 如果要移除的数量大于等于现有数量，直接移除整个物品
      _items.removeAt(existingIndex);
    } else {
      // 否则减少数量
      final updatedItem = _createUpdatedItem(existingItem, existingItem.quantity - quantity);
      _items[existingIndex] = updatedItem;
    }

    return true;
  }

  /// 创建更新数量后的物品
  InventoryItem _createUpdatedItem(InventoryItem item, int newQuantity) {
    switch (item.type) {
      case InventoryItemType.weapon:
        final weaponItem = item as WeaponItem;
        return WeaponItem(
          weapon: weaponItem.weapon,
          quantity: newQuantity,
        );
      default:
        throw UnimplementedError('Item type ${item.type} not implemented yet');
    }
  }

  /// 清空库存
  void clear() {
    _items.clear();
  }

  /// 按类型过滤物品
  List<InventoryItem> getItemsByType(InventoryItemType type) {
    return _items.where((item) => item.type == type).toList();
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'items': _items.map((item) => item.toJson()).toList(),
      'maxSlots': maxSlots,
    };
  }

  /// 从JSON创建库存
  factory Inventory.fromJson(Map<String, dynamic> json) {
    final itemsJson = json['items'] as List<dynamic>? ?? [];
    final items = itemsJson
        .map((itemJson) => InventoryItem.fromJson(itemJson as Map<String, dynamic>))
        .toList();

    return Inventory(
      items: items,
      maxSlots: json['maxSlots'] ?? 50,
    );
  }

  @override
  String toString() {
    return 'Inventory{items: ${_items.length}, usedSlots: $usedSlots/$maxSlots}';
  }
}
