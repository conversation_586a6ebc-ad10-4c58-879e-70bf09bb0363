import 'package:flutter/material.dart';
import '../models/character.dart';
import '../models/player_status.dart';
import '../models/weapon.dart';
import '../models/inventory.dart';
import '../services/logger_service.dart';
import '../services/weapon_service.dart';
import '../services/currency_service.dart';
import '../widgets/location_bar.dart';
import '../widgets/team_bar.dart';
import '../widgets/shop_action_bar.dart';
import '../widgets/weapon_card.dart';
import '../widgets/weapon_detail_dialog.dart';

/// 武器店主界面
/// 提供武器购买、出售和查看功能
class WeaponShopScreen extends StatefulWidget {
  final Character character;

  const WeaponShopScreen({
    super.key,
    required this.character,
  });

  @override
  State<WeaponShopScreen> createState() => _WeaponShopScreenState();
}

class _WeaponShopScreenState extends State<WeaponShopScreen>
    with SingleTickerProviderStateMixin {
  late PlayerStatus _playerStatus;
  late TabController _tabController;
  
  List<Weapon> _shopWeapons = [];
  List<WeaponItem> _playerWeapons = [];
  Weapon? _selectedWeapon;
  bool _isLoading = false;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializePlayerStatus();
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(_onTabChanged);
    _initializeShop();
  }

  @override
  void dispose() {
    _tabController.removeListener(_onTabChanged);
    _tabController.dispose();
    super.dispose();
  }

  /// 初始化玩家状态
  void _initializePlayerStatus() {
    _playerStatus = PlayerStatus(
      character: widget.character,
      location: 'weapon_shop',
    );
    
    LoggerService.gameEvent('player_enter_weapon_shop', {
      'characterId': widget.character.id,
      'characterName': widget.character.name,
      'currency': widget.character.currency,
    });
  }

  /// 初始化商店
  Future<void> _initializeShop() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 确保武器服务已初始化
      await WeaponService.instance.initialize();
      
      // 获取商店武器
      _shopWeapons = WeaponService.instance.getShopWeapons(
        characterLevel: widget.character.level,
        characterStrength: widget.character.attributes.strength,
        characterDexterity: widget.character.attributes.dexterity,
        characterClass: widget.character.characterClass.displayName,
        count: 10,
      );
      
      // 获取玩家武器
      _playerWeapons = widget.character.inventory.weapons;
      
      setState(() {
        _isInitialized = true;
      });
      
      LoggerService.info(LogTags.game, 'Weapon shop initialized', {
        'shopWeapons': _shopWeapons.length,
        'playerWeapons': _playerWeapons.length,
      });
      
    } catch (e, stackTrace) {
      LoggerService.error(LogTags.game, 'Failed to initialize weapon shop', e, stackTrace);
      _showErrorSnackBar('初始化武器店失败，请重试');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 标签页切换
  void _onTabChanged() {
    setState(() {
      _selectedWeapon = null;
    });
    
    LoggerService.userAction('WeaponShopScreen', 'tab_changed', {
      'tabIndex': _tabController.index,
      'tabName': _tabController.index == 0 ? 'buy' : 'sell',
    });
  }

  /// 选择武器
  void _selectWeapon(Weapon weapon) {
    LoggerService.userAction('WeaponShopScreen', 'select_weapon', {
      'weaponId': weapon.id,
      'weaponName': weapon.name,
      'isBuyMode': _isBuyMode,
    });

    setState(() {
      if (_selectedWeapon?.id == weapon.id) {
        // 如果点击已选中的武器，则取消选中
        _selectedWeapon = null;
      } else {
        // 选中新武器
        _selectedWeapon = weapon;
      }
    });
  }

  /// 显示武器详情
  void _showWeaponDetail(Weapon weapon) {
    showDialog(
      context: context,
      builder: (context) => WeaponDetailDialog(
        weapon: weapon,
        character: widget.character,
        isForSale: _isBuyMode,
        onPurchase: _isBuyMode ? () => _purchaseWeapon(weapon) : null,
        onSell: !_isBuyMode ? () => _sellWeapon(weapon) : null,
      ),
    );
  }

  /// 购买武器
  Future<void> _purchaseWeapon(Weapon weapon) async {
    if (_selectedWeapon?.id != weapon.id) {
      setState(() {
        _selectedWeapon = weapon;
      });
    }

    // 关闭详情弹窗
    Navigator.of(context).pop();
    
    await _executePurchase();
  }

  /// 执行购买
  Future<void> _executePurchase() async {
    if (_selectedWeapon == null) return;

    final weapon = _selectedWeapon!;
    
    // 检查金币是否充足
    if (!widget.character.canAfford(weapon.price)) {
      _showErrorSnackBar('金币不足，无法购买');
      return;
    }

    // 检查背包空间
    if (!widget.character.inventory.hasSpace(1)) {
      _showErrorSnackBar('背包空间不足，无法购买');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      LoggerService.userAction('WeaponShopScreen', 'purchase_weapon', {
        'weaponId': weapon.id,
        'weaponName': weapon.name,
        'price': weapon.price,
        'beforeCurrency': widget.character.currency,
      });

      // 扣除金币
      widget.character.purchaseItem(weapon.price);
      
      // 添加武器到背包
      final weaponItem = WeaponItem(weapon: weapon);
      widget.character.inventory.addItem(weaponItem);
      
      // 更新玩家武器列表
      _playerWeapons = widget.character.inventory.weapons;
      
      // 记录货币交易
      CurrencyService.instance.logTransaction(
        playerMoney: widget.character.currency + weapon.price,
        transactionAmount: weapon.price,
        isPurchase: true,
        itemName: weapon.name,
      );
      
      _showSuccessSnackBar('成功购买 ${weapon.name}');
      
      setState(() {
        _selectedWeapon = null;
      });
      
    } catch (e, stackTrace) {
      LoggerService.error(LogTags.game, 'Failed to purchase weapon', e, stackTrace);
      _showErrorSnackBar('购买失败，请重试');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 出售武器
  Future<void> _sellWeapon(Weapon weapon) async {
    if (_selectedWeapon?.id != weapon.id) {
      setState(() {
        _selectedWeapon = weapon;
      });
    }

    // 关闭详情弹窗
    Navigator.of(context).pop();
    
    await _executeSale();
  }

  /// 执行出售
  Future<void> _executeSale() async {
    if (_selectedWeapon == null) return;

    final weapon = _selectedWeapon!;
    
    setState(() {
      _isLoading = true;
    });

    try {
      LoggerService.userAction('WeaponShopScreen', 'sell_weapon', {
        'weaponId': weapon.id,
        'weaponName': weapon.name,
        'sellPrice': weapon.sellPrice,
        'beforeCurrency': widget.character.currency,
      });

      // 从背包移除武器
      widget.character.inventory.removeItem(weapon.id);
      
      // 获得金币
      widget.character.sellItem(weapon.sellPrice);
      
      // 更新玩家武器列表
      _playerWeapons = widget.character.inventory.weapons;
      
      // 记录货币交易
      CurrencyService.instance.logTransaction(
        playerMoney: widget.character.currency - weapon.sellPrice,
        transactionAmount: weapon.sellPrice,
        isPurchase: false,
        itemName: weapon.name,
      );
      
      _showSuccessSnackBar('成功出售 ${weapon.name}');
      
      setState(() {
        _selectedWeapon = null;
      });
      
    } catch (e, stackTrace) {
      LoggerService.error(LogTags.game, 'Failed to sell weapon', e, stackTrace);
      _showErrorSnackBar('出售失败，请重试');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 返回广场
  void _returnToSquare() {
    LoggerService.userAction('WeaponShopScreen', 'return_to_square');
    Navigator.of(context).pop();
  }

  /// 是否为购买模式
  bool get _isBuyMode => _tabController.index == 0;

  /// 显示成功消息
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// 显示错误消息
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            // 1. 地点条（顶部固定）
            LocationBar(
              icon: '🗡️',
              locationName: '武器店',
              onBackPressed: _returnToSquare,
            ),
            
            // 2. 切页按钮
            Container(
              color: Theme.of(context).colorScheme.surface,
              child: TabBar(
                controller: _tabController,
                tabs: const [
                  Tab(
                    icon: Icon(Icons.shopping_cart),
                    text: '购买',
                  ),
                  Tab(
                    icon: Icon(Icons.sell),
                    text: '出售',
                  ),
                ],
              ),
            ),
            
            // 3. 内容区（中间区域，可滚动）
            Expanded(
              child: _isLoading && !_isInitialized
                  ? const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CircularProgressIndicator(),
                          SizedBox(height: 16),
                          Text('正在加载武器店...'),
                        ],
                      ),
                    )
                  : TabBarView(
                      controller: _tabController,
                      children: [
                        // 购买页面
                        _buildWeaponList(_shopWeapons.map((w) => w).toList(), true),
                        // 出售页面
                        _buildWeaponList(_playerWeapons.map((wi) => wi.weapon).toList(), false),
                      ],
                    ),
            ),
            
            // 4. 队伍条（底部固定）
            TeamBar(
              playerStatus: _playerStatus,
              teammates: const [], // 暂时没有队友
            ),
            
            // 5. 操作区（最底部固定）
            ShopActionBar(
              selectedWeapon: _selectedWeapon,
              isLoading: _isLoading,
              isBuyMode: _isBuyMode,
              onPurchase: _executePurchase,
              onSell: _executeSale,
              onBack: _returnToSquare,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建武器列表
  Widget _buildWeaponList(List<Weapon> weapons, bool isForSale) {
    if (weapons.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              isForSale ? Icons.store : Icons.inventory,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              isForSale ? '暂无可购买的武器' : '背包中没有武器',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: weapons.length,
      itemBuilder: (context, index) {
        final weapon = weapons[index];
        return WeaponCard(
          weapon: weapon,
          character: widget.character,
          isSelected: _selectedWeapon?.id == weapon.id,
          isForSale: isForSale,
          onTap: () => _selectWeapon(weapon),
        );
      },
    );
  }
}
