import 'dart:convert';
import 'dart:math';
import 'package:flutter/services.dart';
import 'package:csv/csv.dart';
import '../models/weapon.dart';
import 'logger_service.dart';

/// 武器数据服务
/// 负责加载和管理武器数据
class WeaponService {
  static WeaponService? _instance;
  static WeaponService get instance => _instance ??= WeaponService._();
  
  WeaponService._();

  List<Weapon>? _allWeapons;
  final Random _random = Random();

  /// 初始化武器数据
  Future<void> initialize() async {
    if (_allWeapons != null) return;

    try {
      LoggerService.info(LogTags.game, 'Loading weapon data from CSV');
      
      // 从assets加载CSV文件
      final csvString = await rootBundle.loadString('assets/data/weapons.csv');
      
      // 解析CSV
      final List<List<dynamic>> csvData = const CsvToListConverter().convert(csvString);
      
      // 跳过标题行，转换为Weapon对象
      _allWeapons = csvData.skip(1).map((row) {
        try {
          final stringRow = row.map((cell) => cell.toString()).toList();
          return Weapon.fromCsvRow(stringRow);
        } catch (e, stackTrace) {
          LoggerService.error(LogTags.game, 'Failed to parse weapon row: $row', e, stackTrace);
          rethrow;
        }
      }).toList();

      LoggerService.info(LogTags.game, 'Loaded ${_allWeapons!.length} weapons');
      
    } catch (e, stackTrace) {
      LoggerService.error(LogTags.game, 'Failed to load weapon data', e, stackTrace);
      rethrow;
    }
  }

  /// 获取所有武器
  List<Weapon> getAllWeapons() {
    if (_allWeapons == null) {
      throw StateError('WeaponService not initialized. Call initialize() first.');
    }
    return List.unmodifiable(_allWeapons!);
  }

  /// 根据ID获取武器
  Weapon? getWeaponById(String id) {
    if (_allWeapons == null) return null;
    
    try {
      return _allWeapons!.firstWhere((weapon) => weapon.id == id);
    } catch (e) {
      LoggerService.warning(LogTags.game, 'Weapon not found: $id');
      return null;
    }
  }

  /// 获取随机武器列表（用于商店显示）
  List<Weapon> getRandomWeapons({int count = 10}) {
    if (_allWeapons == null || _allWeapons!.isEmpty) {
      return [];
    }

    final List<Weapon> shuffled = List.from(_allWeapons!);
    shuffled.shuffle(_random);
    
    return shuffled.take(count).toList();
  }

  /// 根据品质过滤武器
  List<Weapon> getWeaponsByQuality(WeaponQuality quality) {
    if (_allWeapons == null) return [];
    
    return _allWeapons!.where((weapon) => weapon.quality == quality).toList();
  }

  /// 根据职业过滤武器
  List<Weapon> getWeaponsByClass(String characterClass) {
    if (_allWeapons == null) return [];
    
    return _allWeapons!.where((weapon) {
      // 如果武器没有职业限制，任何职业都可以使用
      if (weapon.requiredClasses.isEmpty) return true;
      
      // 检查是否包含指定职业
      return weapon.requiredClasses.contains(characterClass);
    }).toList();
  }

  /// 根据等级过滤武器
  List<Weapon> getWeaponsByLevel(int characterLevel) {
    if (_allWeapons == null) return [];
    
    return _allWeapons!.where((weapon) => weapon.requiredLevel <= characterLevel).toList();
  }

  /// 获取适合角色的武器
  List<Weapon> getWeaponsForCharacter({
    required int level,
    required int strength,
    required int dexterity,
    required String characterClass,
  }) {
    if (_allWeapons == null) return [];
    
    return _allWeapons!.where((weapon) {
      return weapon.canEquip(
        characterLevel: level,
        characterStrength: strength,
        characterDexterity: dexterity,
        characterClass: characterClass,
      );
    }).toList();
  }

  /// 获取商店武器列表（随机10个，适合当前角色等级）
  List<Weapon> getShopWeapons({
    required int characterLevel,
    required int characterStrength,
    required int characterDexterity,
    required String characterClass,
    int count = 10,
  }) {
    LoggerService.info(LogTags.game, 'Getting shop weapons for character', {
      'level': characterLevel,
      'strength': characterStrength,
      'dexterity': characterDexterity,
      'class': characterClass,
      'count': count,
    });

    // 获取适合角色的武器
    final suitableWeapons = getWeaponsForCharacter(
      level: characterLevel,
      strength: characterStrength,
      dexterity: characterDexterity,
      characterClass: characterClass,
    );

    // 如果适合的武器不足，添加一些低等级的通用武器
    final allSuitableWeapons = List<Weapon>.from(suitableWeapons);
    if (allSuitableWeapons.length < count) {
      final lowLevelWeapons = _allWeapons!.where((weapon) {
        return weapon.requiredLevel <= 3 && 
               weapon.requiredClasses.isEmpty &&
               !allSuitableWeapons.contains(weapon);
      }).toList();
      
      allSuitableWeapons.addAll(lowLevelWeapons);
    }

    // 随机选择
    allSuitableWeapons.shuffle(_random);
    final result = allSuitableWeapons.take(count).toList();

    LoggerService.info(LogTags.game, 'Generated ${result.length} shop weapons');
    return result;
  }

  /// 搜索武器
  List<Weapon> searchWeapons(String query) {
    if (_allWeapons == null || query.isEmpty) return [];
    
    final lowerQuery = query.toLowerCase();
    return _allWeapons!.where((weapon) {
      return weapon.name.toLowerCase().contains(lowerQuery) ||
             weapon.specialEffect.toLowerCase().contains(lowerQuery);
    }).toList();
  }

  /// 按攻击力排序武器
  List<Weapon> sortWeaponsByAttack({bool descending = true}) {
    if (_allWeapons == null) return [];
    
    final sorted = List<Weapon>.from(_allWeapons!);
    sorted.sort((a, b) => descending ? b.attack.compareTo(a.attack) : a.attack.compareTo(b.attack));
    return sorted;
  }

  /// 按价格排序武器
  List<Weapon> sortWeaponsByPrice({bool descending = true}) {
    if (_allWeapons == null) return [];
    
    final sorted = List<Weapon>.from(_allWeapons!);
    sorted.sort((a, b) => descending ? b.price.compareTo(a.price) : a.price.compareTo(b.price));
    return sorted;
  }

  /// 重新加载武器数据
  Future<void> reload() async {
    _allWeapons = null;
    await initialize();
  }

  /// 获取武器统计信息
  Map<String, dynamic> getWeaponStats() {
    if (_allWeapons == null) return {};

    final qualityCount = <String, int>{};
    int totalAttack = 0;
    int totalPrice = 0;

    for (final weapon in _allWeapons!) {
      qualityCount[weapon.quality.displayName] = 
          (qualityCount[weapon.quality.displayName] ?? 0) + 1;
      totalAttack += weapon.attack;
      totalPrice += weapon.price;
    }

    return {
      'totalWeapons': _allWeapons!.length,
      'qualityDistribution': qualityCount,
      'averageAttack': totalAttack / _allWeapons!.length,
      'averagePrice': totalPrice / _allWeapons!.length,
    };
  }
}
