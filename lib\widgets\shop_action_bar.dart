import 'package:flutter/material.dart';
import '../models/weapon.dart';

/// 武器店操作栏组件
/// 显示武器店特有的操作按钮
class ShopActionBar extends StatelessWidget {
  final Weapon? selectedWeapon;
  final bool isLoading;
  final bool isBuyMode;
  final VoidCallback? onPurchase;
  final VoidCallback? onSell;
  final VoidCallback onBack;

  const ShopActionBar({
    super.key,
    this.selectedWeapon,
    this.isLoading = false,
    this.isBuyMode = true,
    this.onPurchase,
    this.onSell,
    required this.onBack,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          // 返回按钮
          Expanded(
            child: OutlinedButton.icon(
              onPressed: isLoading ? null : onBack,
              icon: const Icon(Icons.arrow_back),
              label: const Text('返回广场'),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
          
          const SizedBox(width: 12),
          
          // 主要操作按钮（购买或出售）
          if (selectedWeapon != null) ...[
            Expanded(
              flex: 2,
              child: isBuyMode
                  ? ElevatedButton.icon(
                      onPressed: isLoading ? null : onPurchase,
                      icon: isLoading
                          ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : const Icon(Icons.shopping_cart),
                      label: Text(isLoading ? '购买中...' : '购买武器'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    )
                  : ElevatedButton.icon(
                      onPressed: isLoading ? null : onSell,
                      icon: isLoading
                          ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : const Icon(Icons.sell),
                      label: Text(isLoading ? '出售中...' : '出售武器'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
            ),
          ] else ...[
            // 没有选中武器时显示提示
            Expanded(
              flex: 2,
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: Colors.grey.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Colors.grey.withOpacity(0.3),
                  ),
                ),
                child: Center(
                  child: Text(
                    isBuyMode ? '请选择要购买的武器' : '请选择要出售的武器',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}
