import 'package:flutter/material.dart';
import '../models/weapon.dart';
import '../models/character.dart';
import '../services/currency_service.dart';

/// 武器卡片组件
/// 显示武器的基本信息和属性
class WeaponCard extends StatelessWidget {
  final Weapon weapon;
  final Character? character;
  final bool isSelected;
  final bool isForSale;
  final VoidCallback? onTap;

  const WeaponCard({
    super.key,
    required this.weapon,
    this.character,
    this.isSelected = false,
    this.isForSale = true,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final canEquip = character?.canEquipWeapon(weapon) ?? true;
    final price = isForSale ? weapon.price : weapon.sellPrice;
    
    return Card(
      elevation: isSelected ? 8 : 2,
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      color: isSelected 
          ? Theme.of(context).colorScheme.primaryContainer
          : null,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 武器标题行
              Row(
                children: [
                  // 武器图标
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Color(weapon.quality.color).withOpacity(0.2),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: Color(weapon.quality.color),
                        width: 2,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        weapon.icon,
                        style: const TextStyle(fontSize: 20),
                      ),
                    ),
                  ),
                  
                  const SizedBox(width: 12),
                  
                  // 武器名称和品质
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          weapon.name,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: canEquip ? null : Colors.grey,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 2),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: Color(weapon.quality.color).withOpacity(0.2),
                            borderRadius: BorderRadius.circular(4),
                            border: Border.all(
                              color: Color(weapon.quality.color),
                              width: 1,
                            ),
                          ),
                          child: Text(
                            weapon.quality.displayName,
                            style: TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                              color: Color(weapon.quality.color),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  // 价格
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        CurrencyService.instance.getShopPriceDisplay(price),
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: isForSale ? Colors.green : Colors.orange,
                        ),
                      ),
                      if (!isForSale)
                        Text(
                          '出售价',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey,
                          ),
                        ),
                    ],
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // 武器属性
              Row(
                children: [
                  _buildAttributeChip(
                    context,
                    icon: '⚔️',
                    label: '攻击',
                    value: weapon.attack.toString(),
                    color: Colors.red,
                  ),
                  const SizedBox(width: 8),
                  _buildAttributeChip(
                    context,
                    icon: '🛡️',
                    label: '防御',
                    value: weapon.defense.toString(),
                    color: Colors.blue,
                  ),
                  const SizedBox(width: 8),
                  _buildAttributeChip(
                    context,
                    icon: '⚡',
                    label: '速度',
                    value: weapon.speed >= 0 ? '+${weapon.speed}' : weapon.speed.toString(),
                    color: Colors.green,
                  ),
                  const SizedBox(width: 8),
                  _buildAttributeChip(
                    context,
                    icon: '💥',
                    label: '暴击',
                    value: '${weapon.critical}%',
                    color: Colors.orange,
                  ),
                ],
              ),
              
              const SizedBox(height: 8),
              
              // 特殊效果
              if (weapon.specialEffect.isNotEmpty) ...[
                Text(
                  weapon.specialEffect,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                    fontStyle: FontStyle.italic,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),
              ],
              
              // 装备要求
              if (_hasRequirements()) ...[
                Wrap(
                  spacing: 4,
                  runSpacing: 4,
                  children: [
                    if (weapon.requiredLevel > 1)
                      _buildRequirementChip(
                        context,
                        '等级 ${weapon.requiredLevel}',
                        _meetsLevelRequirement(),
                      ),
                    if (weapon.requiredStrength > 0)
                      _buildRequirementChip(
                        context,
                        '力量 ${weapon.requiredStrength}',
                        _meetsStrengthRequirement(),
                      ),
                    if (weapon.requiredDexterity > 0)
                      _buildRequirementChip(
                        context,
                        '敏捷 ${weapon.requiredDexterity}',
                        _meetsDexterityRequirement(),
                      ),
                    if (weapon.requiredClasses.isNotEmpty)
                      _buildRequirementChip(
                        context,
                        weapon.requiredClasses.join('、'),
                        _meetsClassRequirement(),
                      ),
                  ],
                ),
              ],
              
              // 装备状态提示
              if (!canEquip) ...[
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.red.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(color: Colors.red.withOpacity(0.3)),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(Icons.warning, color: Colors.red, size: 16),
                      const SizedBox(width: 4),
                      Text(
                        '不满足装备要求',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.red,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// 构建属性芯片
  Widget _buildAttributeChip(
    BuildContext context, {
    required String icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(icon, style: const TextStyle(fontSize: 10)),
          const SizedBox(width: 2),
          Text(
            value,
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建需求芯片
  Widget _buildRequirementChip(BuildContext context, String text, bool isMet) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: isMet ? Colors.green.withOpacity(0.1) : Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(
          color: isMet ? Colors.green.withOpacity(0.3) : Colors.red.withOpacity(0.3),
        ),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 10,
          color: isMet ? Colors.green : Colors.red,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  /// 检查是否有装备要求
  bool _hasRequirements() {
    return weapon.requiredLevel > 1 ||
           weapon.requiredStrength > 0 ||
           weapon.requiredDexterity > 0 ||
           weapon.requiredClasses.isNotEmpty;
  }

  /// 检查等级要求
  bool _meetsLevelRequirement() {
    return (character?.level ?? 1) >= weapon.requiredLevel;
  }

  /// 检查力量要求
  bool _meetsStrengthRequirement() {
    return (character?.attributes.strength ?? 0) >= weapon.requiredStrength;
  }

  /// 检查敏捷要求
  bool _meetsDexterityRequirement() {
    return (character?.attributes.dexterity ?? 0) >= weapon.requiredDexterity;
  }

  /// 检查职业要求
  bool _meetsClassRequirement() {
    if (weapon.requiredClasses.isEmpty) return true;
    return weapon.requiredClasses.contains(character?.characterClass.displayName ?? '');
  }
}
