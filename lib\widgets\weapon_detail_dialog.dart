import 'package:flutter/material.dart';
import '../models/weapon.dart';
import '../models/character.dart';
import '../services/currency_service.dart';

/// 武器详情弹窗
/// 显示武器的完整信息和属性
class WeaponDetailDialog extends StatelessWidget {
  final Weapon weapon;
  final Character? character;
  final bool isForSale;
  final VoidCallback? onPurchase;
  final VoidCallback? onSell;

  const WeaponDetailDialog({
    super.key,
    required this.weapon,
    this.character,
    this.isForSale = true,
    this.onPurchase,
    this.onSell,
  });

  @override
  Widget build(BuildContext context) {
    final canEquip = character?.canEquipWeapon(weapon) ?? true;
    final price = isForSale ? weapon.price : weapon.sellPrice;
    final canAfford = character?.canAfford(price) ?? true;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        constraints: const BoxConstraints(maxWidth: 400, maxHeight: 600),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题栏
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Color(weapon.quality.color).withOpacity(0.1),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
                border: Border.all(
                  color: Color(weapon.quality.color).withOpacity(0.3),
                ),
              ),
              child: Row(
                children: [
                  // 武器图标
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      color: Color(weapon.quality.color).withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Color(weapon.quality.color),
                        width: 2,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        weapon.icon,
                        style: const TextStyle(fontSize: 24),
                      ),
                    ),
                  ),
                  
                  const SizedBox(width: 16),
                  
                  // 武器名称和品质
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          weapon.name,
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Color(weapon.quality.color).withOpacity(0.2),
                            borderRadius: BorderRadius.circular(6),
                            border: Border.all(
                              color: Color(weapon.quality.color),
                              width: 1,
                            ),
                          ),
                          child: Text(
                            weapon.quality.displayName,
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              color: Color(weapon.quality.color),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  // 关闭按钮
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
            ),
            
            // 内容区域
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 价格信息
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: (isForSale ? Colors.green : Colors.orange).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: (isForSale ? Colors.green : Colors.orange).withOpacity(0.3),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            isForSale ? '购买价格' : '出售价格',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            CurrencyService.instance.getShopPriceDisplay(price),
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: isForSale ? Colors.green : Colors.orange,
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // 武器属性
                    Text(
                      '武器属性',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    
                    Row(
                      children: [
                        Expanded(
                          child: _buildAttributeCard(
                            context,
                            icon: '⚔️',
                            label: '攻击力',
                            value: weapon.attack.toString(),
                            color: Colors.red,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: _buildAttributeCard(
                            context,
                            icon: '🛡️',
                            label: '防御力',
                            value: weapon.defense.toString(),
                            color: Colors.blue,
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 8),
                    
                    Row(
                      children: [
                        Expanded(
                          child: _buildAttributeCard(
                            context,
                            icon: '⚡',
                            label: '速度',
                            value: weapon.speed >= 0 ? '+${weapon.speed}' : weapon.speed.toString(),
                            color: Colors.green,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: _buildAttributeCard(
                            context,
                            icon: '💥',
                            label: '暴击率',
                            value: '${weapon.critical}%',
                            color: Colors.orange,
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // 特殊效果
                    if (weapon.specialEffect.isNotEmpty) ...[
                      Text(
                        '特殊效果',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.purple.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: Colors.purple.withOpacity(0.3),
                          ),
                        ),
                        child: Text(
                          weapon.specialEffect,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                    ],
                    
                    // 装备要求
                    if (_hasRequirements()) ...[
                      Text(
                        '装备要求',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      
                      if (weapon.requiredLevel > 1)
                        _buildRequirementRow(
                          context,
                          '等级要求',
                          '${weapon.requiredLevel}级',
                          _meetsLevelRequirement(),
                        ),
                      
                      if (weapon.requiredStrength > 0)
                        _buildRequirementRow(
                          context,
                          '力量要求',
                          '${weapon.requiredStrength}点',
                          _meetsStrengthRequirement(),
                        ),
                      
                      if (weapon.requiredDexterity > 0)
                        _buildRequirementRow(
                          context,
                          '敏捷要求',
                          '${weapon.requiredDexterity}点',
                          _meetsDexterityRequirement(),
                        ),
                      
                      if (weapon.requiredClasses.isNotEmpty)
                        _buildRequirementRow(
                          context,
                          '职业要求',
                          weapon.requiredClasses.join('、'),
                          _meetsClassRequirement(),
                        ),
                      
                      const SizedBox(height: 16),
                    ],
                    
                    // 装备状态提示
                    if (!canEquip) ...[
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.red.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.red.withOpacity(0.3)),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.warning, color: Colors.red),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                '当前角色不满足装备要求',
                                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  color: Colors.red,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 16),
                    ],
                    
                    // 金钱不足提示
                    if (isForSale && !canAfford) ...[
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.orange.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.orange.withOpacity(0.3)),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.money_off, color: Colors.orange),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                '金币不足，无法购买',
                                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  color: Colors.orange,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
            
            // 操作按钮
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border(
                  top: BorderSide(
                    color: Colors.grey.withOpacity(0.3),
                  ),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text('关闭'),
                    ),
                  ),
                  
                  const SizedBox(width: 12),
                  
                  if (isForSale && onPurchase != null)
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: canAfford ? onPurchase : null,
                        icon: const Icon(Icons.shopping_cart),
                        label: const Text('购买'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                  
                  if (!isForSale && onSell != null)
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: onSell,
                        icon: const Icon(Icons.sell),
                        label: const Text('出售'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建属性卡片
  Widget _buildAttributeCard(
    BuildContext context, {
    required String icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Text(icon, style: const TextStyle(fontSize: 20)),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建需求行
  Widget _buildRequirementRow(
    BuildContext context,
    String label,
    String value,
    bool isMet,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          Row(
            children: [
              Text(
                value,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(width: 8),
              Icon(
                isMet ? Icons.check_circle : Icons.cancel,
                color: isMet ? Colors.green : Colors.red,
                size: 16,
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 检查是否有装备要求
  bool _hasRequirements() {
    return weapon.requiredLevel > 1 ||
           weapon.requiredStrength > 0 ||
           weapon.requiredDexterity > 0 ||
           weapon.requiredClasses.isNotEmpty;
  }

  /// 检查等级要求
  bool _meetsLevelRequirement() {
    return (character?.level ?? 1) >= weapon.requiredLevel;
  }

  /// 检查力量要求
  bool _meetsStrengthRequirement() {
    return (character?.attributes.strength ?? 0) >= weapon.requiredStrength;
  }

  /// 检查敏捷要求
  bool _meetsDexterityRequirement() {
    return (character?.attributes.dexterity ?? 0) >= weapon.requiredDexterity;
  }

  /// 检查职业要求
  bool _meetsClassRequirement() {
    if (weapon.requiredClasses.isEmpty) return true;
    return weapon.requiredClasses.contains(character?.characterClass.displayName ?? '');
  }
}
